"use server"

import { db } from "@/lib/server/db"
import { boxobanUserData, boxobanLevels } from "@/lib/server/db/schema"
import { authActionClient } from "@/lib/server/safe-action"
import { eq, and, sql } from "drizzle-orm"
import { revalidatePath } from "next/cache"
import { z } from "zod"

const setModeSchema = z.object({
  mode: z.enum(["medium", "hard", "unfiltered"]),
})

export const setBoxobanMode = authActionClient
  .metadata({ actionName: "setBoxobanMode" })
  .schema(setModeSchema)
  .action(async ({ parsedInput, ctx }) => {
    const { mode } = parsedInput
    const { user } = ctx

    // Upsert user data with new mode
    await db
      .insert(boxobanUserData)
      .values({
        userId: user.id,
        mode,
        updatedAt: new Date(),
      })
      .onConflictDoUpdate({
        target: boxobanUserData.userId,
        set: {
          mode,
          updatedAt: new Date(),
        },
      })

    revalidatePath("/boxoban")
    return { success: true }
  })

const refreshRecordsSchema = z.object({})

export const refreshBoxobanRecords = authActionClient
  .metadata({ actionName: "refreshBoxobanRecords" })
  .schema(refreshRecordsSchema)
  .action(async () => {
    // Simply revalidate the path to refresh the data
    revalidatePath("/boxoban")
    return { success: true }
  })

// Action to get or assign a new level for the user
export const getNextBoxobanLevel = authActionClient
  .metadata({ actionName: "getNextBoxobanLevel" })
  .action(async ({ ctx }) => {
    const { user } = ctx

    // Get user's current mode
    const [userData] = await db
      .select()
      .from(boxobanUserData)
      .where(eq(boxobanUserData.userId, user.id))
      .limit(1)

    const mode = userData?.mode || "medium"

    // Check if user has a current level assigned
    if (userData?.currentLevelId) {
      const [currentLevel] = await db
        .select()
        .from(boxobanLevels)
        .where(eq(boxobanLevels.levelId, userData.currentLevelId))
        .limit(1)

      if (currentLevel && currentLevel.status === "assigned") {
        return {
          levelId: currentLevel.levelId,
          category: currentLevel.category,
          fileNumber: currentLevel.fileNumber,
          levelNumber: currentLevel.levelNumber,
        }
      }
    }

    // Find an available level in the user's mode
    const [availableLevel] = await db
      .select()
      .from(boxobanLevels)
      .where(
        and(
          eq(boxobanLevels.category, mode),
          eq(boxobanLevels.status, "available")
        )
      )
      .limit(1)

    if (!availableLevel) {
      throw new Error(`No available levels in ${mode} category`)
    }

    // Assign the level to the user
    await db.transaction(async (tx) => {
      // Update the level status
      await tx
        .update(boxobanLevels)
        .set({
          assignedTo: user.id,
          status: "assigned",
          updatedAt: new Date(),
        })
        .where(eq(boxobanLevels.levelId, availableLevel.levelId))

      // Update user's current level
      await tx
        .insert(boxobanUserData)
        .values({
          userId: user.id,
          currentLevelId: availableLevel.levelId,
          mode,
          updatedAt: new Date(),
        })
        .onConflictDoUpdate({
          target: boxobanUserData.userId,
          set: {
            currentLevelId: availableLevel.levelId,
            mode,
            updatedAt: new Date(),
          },
        })
    })

    revalidatePath("/boxoban")
    
    return {
      levelId: availableLevel.levelId,
      category: availableLevel.category,
      fileNumber: availableLevel.fileNumber,
      levelNumber: availableLevel.levelNumber,
    }
  })
