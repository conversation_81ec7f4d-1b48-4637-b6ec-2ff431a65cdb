import { db } from "@/lib/server/db"
import { 
  boxobanUserData, 
  boxobanGlobalProgress, 
  boxobanLevels,
  userTable 
} from "@/lib/server/db/schema"
import { eq, and, sql, desc, asc } from "drizzle-orm"
import { PAGE_SIZE } from "@/lib/common/constants"

export async function getBoxobanUserData(userId: number) {
  const [userData] = await db
    .select()
    .from(boxobanUserData)
    .where(eq(boxobanUserData.userId, userId))
    .limit(1)

  return userData || null
}

export async function getBoxobanGlobalProgress() {
  const progress = await db
    .select()
    .from(boxobanGlobalProgress)
    .orderBy(boxobanGlobalProgress.category)

  return progress
}

export async function getBoxobanRecords({
  userId,
  page = 1,
  category,
  sortBy = "completedAt",
  sortOrder = "desc",
}: {
  userId: number
  page?: number
  category?: "medium" | "hard" | "unfiltered"
  sortBy?: "completedAt" | "steps" | "timeMs"
  sortOrder?: "asc" | "desc"
}) {
  const offset = (page - 1) * PAGE_SIZE

  // Build the base query - get solved levels for this user
  let baseQuery = and(
    eq(boxobanLevels.assignedTo, userId),
    eq(boxobanLevels.status, "solved")
  )

  // Add category filter if provided
  if (category) {
    baseQuery = and(baseQuery, eq(boxobanLevels.category, category))
  }

  // Get total count for pagination
  const [{ count }] = await db
    .select({ count: sql<number>`count(*)` })
    .from(boxobanLevels)
    .where(baseQuery)

  // Determine sort column and order
  let orderByClause
  const orderDirection = sortOrder === "desc" ? desc : asc

  switch (sortBy) {
    case "steps":
      // For steps, we need to join with a hypothetical solved levels table
      // Since we don't have that, we'll sort by updatedAt for now
      orderByClause = orderDirection(boxobanLevels.updatedAt)
      break
    case "timeMs":
      // Similar to steps, we'll sort by updatedAt for now
      orderByClause = orderDirection(boxobanLevels.updatedAt)
      break
    case "completedAt":
    default:
      orderByClause = orderDirection(boxobanLevels.updatedAt)
  }

  // Get records with pagination
  const records = await db
    .select({
      levelId: boxobanLevels.levelId,
      category: boxobanLevels.category,
      fileNumber: boxobanLevels.fileNumber,
      levelNumber: boxobanLevels.levelNumber,
      status: boxobanLevels.status,
      updatedAt: boxobanLevels.updatedAt,
    })
    .from(boxobanLevels)
    .where(baseQuery)
    .orderBy(orderByClause)
    .limit(PAGE_SIZE)
    .offset(offset)

  return {
    records,
    count,
    category,
    sortBy,
    sortOrder,
    pagination: {
      totalRecords: Number(count),
      totalPages: Math.ceil(Number(count) / PAGE_SIZE),
      currentPage: page,
      pageSize: PAGE_SIZE,
    },
  }
}

export async function getUserBoxobanStats(userId: number) {
  // Get user's current mode and level
  const userData = await getBoxobanUserData(userId)
  
  // Get counts by category
  const stats = await db
    .select({
      category: boxobanLevels.category,
      count: sql<number>`count(*)`,
    })
    .from(boxobanLevels)
    .where(
      and(
        eq(boxobanLevels.assignedTo, userId),
        eq(boxobanLevels.status, "solved")
      )
    )
    .groupBy(boxobanLevels.category)

  const mediumSolved = stats.find(s => s.category === "medium")?.count || 0
  const hardSolved = stats.find(s => s.category === "hard")?.count || 0
  const unfilteredSolved = stats.find(s => s.category === "unfiltered")?.count || 0
  const totalSolved = mediumSolved + hardSolved + unfilteredSolved

  return {
    userData,
    stats: {
      mediumSolved,
      hardSolved,
      unfilteredSolved,
      totalSolved,
    }
  }
}
