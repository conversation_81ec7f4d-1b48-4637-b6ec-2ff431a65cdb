import { type Metadata } from "next/types"

import {
  withSessionValidatedPage,
  type ValidatedSession,
} from "@/lib/server/auth/with-session-validated"
import { Skeleton } from "@/components/ui/skeleton"
import { Suspense } from "react"
import BoxobanHeader from "./(components)/BoxobanHeader"
import GlobalProgressSection from "./(components)/GlobalProgressSection"
import PersonalProgressSection from "./(components)/PersonalProgressSection"
import EmptyState from "./(components)/EmptyState"
import RecordsTable from "./(components)/RecordsTable"
import { getBoxobanRecords } from "./queries"

export const metadata: Metadata = {
  title: "Boxoban Challenge | Sokoverse",
  description:
    "Join the global Boxoban Challenge powered by DeepMind's dataset. Solve unique levels that are permanently marked as completed once solved.",
}

const mockRecordsData = {
  records: [
    {
      levelId: "medium-42-123",
      category: "medium",
      fileNumber: 42,
      levelNumber: 123,
      status: "solved",
      updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    },
    {
      levelId: "hard-15-67",
      category: "hard",
      fileNumber: 15,
      levelNumber: 67,
      status: "solved",
      updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    },
    {
      levelId: "unfiltered-89-456",
      category: "unfiltered",
      fileNumber: 89,
      levelNumber: 456,
      status: "solved",
      updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3), // 3 days ago
    },
    {
      levelId: "medium-33-789",
      category: "medium",
      fileNumber: 33,
      levelNumber: 789,
      status: "solved",
      updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 1 week ago
    },
  ],
  count: 4,
  pagination: {
    totalRecords: 4,
    totalPages: 1,
    currentPage: 1,
    pageSize: 10,
  },
}

async function BoxobanPage({
  session,
  searchParams,
}: {
  session: ValidatedSession
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const params = await searchParams
  const page = typeof params.page === "string" ? parseInt(params.page) : 1
  const category =
    typeof params.category === "string" ? params.category : undefined
  const sortBy =
    typeof params.sortBy === "string" ? params.sortBy : "completedAt"
  const sortOrder =
    typeof params.sortOrder === "string" ? params.sortOrder : "desc"

  const useMockData = false
  const userId = session.user.id

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <BoxobanHeader />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Suspense fallback={<GlobalProgressSkeleton />}>
          <GlobalProgressSection useMockData={useMockData} />
        </Suspense>

        <Suspense fallback={<PersonalProgressSkeleton />}>
          <PersonalProgressSection userId={userId} useMockData={useMockData} />
        </Suspense>
      </div>

      <Suspense fallback={<RecordsSkeleton />}>
        <BoxobanRecordsSection
          userId={userId}
          page={page}
          category={category}
          sortBy={sortBy}
          sortOrder={sortOrder}
          useMockData={useMockData}
        />
      </Suspense>
    </div>
  )
}

async function BoxobanRecordsSection({
  userId,
  page,
  category,
  sortBy,
  sortOrder,
  useMockData = false,
}: {
  userId: number
  page: number
  category?: string
  sortBy: string
  sortOrder: string
  useMockData?: boolean
}) {
  const recordsData = useMockData
    ? mockRecordsData
    : await getBoxobanRecords({
        userId,
        page,
        category: category as "medium" | "hard" | "unfiltered" | undefined,
        sortBy: sortBy as "completedAt" | "steps" | "timeMs",
        sortOrder: sortOrder as "asc" | "desc",
      })

  if (!recordsData || recordsData.records.length === 0) {
    return <EmptyState />
  }

  return (
    <RecordsTable
      records={recordsData.records}
      currentCategory={category as "medium" | "hard" | "unfiltered" | undefined}
      currentSortBy={sortBy as "completedAt" | "steps" | "timeMs"}
      currentSortOrder={sortOrder as "asc" | "desc"}
      count={recordsData.count}
      currentPage={recordsData.pagination.currentPage}
      totalPages={recordsData.pagination.totalPages}
    />
  )
}

function GlobalProgressSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48" />
      <div className="space-y-3">
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-20 w-full" />
      </div>
    </div>
  )
}

function PersonalProgressSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48" />
      <div className="space-y-3">
        <Skeleton className="h-16 w-full" />
        <Skeleton className="h-12 w-full" />
      </div>
    </div>
  )
}

function RecordsSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-64" />
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    </div>
  )
}
export default withSessionValidatedPage(BoxobanPage)
